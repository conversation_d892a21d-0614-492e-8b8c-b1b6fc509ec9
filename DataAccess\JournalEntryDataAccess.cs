using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Awqaf_Managment.Models.Accounting;
using AwqafManagement.Services;
using Awqaf_Managment.DataAccess;

namespace Awqaf_Managment.DataAccess
{
    public class JournalEntryDataAccess
    {
        private readonly string _connectionString;

        public JournalEntryDataAccess()
        {
            _connectionString = DatabaseConnection.ConnectionString;
        }

        #region Journal Entry CRUD Operations

        /// <summary>
        /// إنشاء قيد يومي جديد
        /// </summary>
        /// <param name="journalEntry">بيانات القيد</param>
        /// <returns>معرف القيد المنشأ</returns>
        public int CreateJournalEntry(JournalEntry journalEntry)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // إدراج القيد الرئيسي
                        string insertJournalSql = @"
                            INSERT INTO JournalEntry
                            (JournalNumber, JournalDate, JournalType, Status, GeneralDescription,
                             TotalDebit, TotalCredit, CostCenterId, CreatedDate, CreatedBy)
                            VALUES
                            (@JournalNumber, @JournalDate, @JournalType, @Status, @GeneralDescription,
                             @TotalDebit, @TotalCredit, @CostCenterId, @CreatedDate, @CreatedBy);
                            SELECT SCOPE_IDENTITY();";

                        int journalEntryId;
                        using (var command = new SqlCommand(insertJournalSql, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalNumber", journalEntry.JournalNumber);
                            command.Parameters.AddWithValue("@JournalDate", journalEntry.JournalDate);
                            command.Parameters.AddWithValue("@JournalType", (int)journalEntry.JournalType);
                            command.Parameters.AddWithValue("@Status", (int)journalEntry.Status);
                            command.Parameters.AddWithValue("@GeneralDescription", journalEntry.GeneralDescription ?? "");
                            command.Parameters.AddWithValue("@TotalDebit", journalEntry.TotalDebit);
                            command.Parameters.AddWithValue("@TotalCredit", journalEntry.TotalCredit);
                            command.Parameters.AddWithValue("@CostCenterId", (object)journalEntry.CostCenterId ?? DBNull.Value);
                            command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                            command.Parameters.AddWithValue("@CreatedBy", "System"); // TODO: استخدام المستخدم الحالي

                            journalEntryId = Convert.ToInt32(command.ExecuteScalar());
                        }

                        // إدراج تفاصيل القيد
                        if (journalEntry.JournalEntryDetails != null && journalEntry.JournalEntryDetails.Any())
                        {
                            string insertDetailsSql = @"
                                INSERT INTO JournalEntryDetails 
                                (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, 
                                 Reference, Description)
                                VALUES 
                                (@JournalEntryId, @AccountId, @LineNumber, @DebitAmount, @CreditAmount, 
                                 @Reference, @Description)";

                            foreach (var detail in journalEntry.JournalEntryDetails)
                            {
                                using (var detailCommand = new SqlCommand(insertDetailsSql, connection, transaction))
                                {
                                    detailCommand.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                                    detailCommand.Parameters.AddWithValue("@AccountId", detail.AccountId);
                                    detailCommand.Parameters.AddWithValue("@LineNumber", detail.LineNumber);
                                    detailCommand.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                                    detailCommand.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                                    detailCommand.Parameters.AddWithValue("@Reference", detail.Reference ?? "");
                                    detailCommand.Parameters.AddWithValue("@Description", detail.Description ?? "");

                                    detailCommand.ExecuteNonQuery();
                                }
                            }
                        }

                        transaction.Commit();
                        return journalEntryId;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// تحديث قيد يومي موجود
        /// </summary>
        /// <param name="journalEntry">بيانات القيد المحدثة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public bool UpdateJournalEntry(JournalEntry journalEntry)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // تحديث القيد الرئيسي
                        string updateJournalSql = @"
                            UPDATE JournalEntry
                            SET JournalNumber = @JournalNumber,
                                JournalDate = @JournalDate,
                                JournalType = @JournalType,
                                GeneralDescription = @GeneralDescription,
                                TotalDebit = @TotalDebit,
                                TotalCredit = @TotalCredit,
                                CostCenterId = @CostCenterId,
                                ModifiedDate = @ModifiedDate,
                                ModifiedBy = @ModifiedBy
                            WHERE JournalEntryId = @JournalEntryId AND Status = @DraftStatus";

                        using (var command = new SqlCommand(updateJournalSql, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalEntryId", journalEntry.JournalEntryId);
                            command.Parameters.AddWithValue("@JournalNumber", journalEntry.JournalNumber);
                            command.Parameters.AddWithValue("@JournalDate", journalEntry.JournalDate);
                            command.Parameters.AddWithValue("@JournalType", (int)journalEntry.JournalType);
                            command.Parameters.AddWithValue("@GeneralDescription", journalEntry.GeneralDescription ?? "");
                            command.Parameters.AddWithValue("@TotalDebit", journalEntry.TotalDebit);
                            command.Parameters.AddWithValue("@TotalCredit", journalEntry.TotalCredit);
                            command.Parameters.AddWithValue("@CostCenterId", (object)journalEntry.CostCenterId ?? DBNull.Value);
                            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            command.Parameters.AddWithValue("@ModifiedBy", "System"); // TODO: استخدام المستخدم الحالي
                            command.Parameters.AddWithValue("@DraftStatus", (int)JournalStatus.Draft);

                            int rowsAffected = command.ExecuteNonQuery();
                            if (rowsAffected == 0)
                                throw new InvalidOperationException("لا يمكن تحديث القيد. قد يكون مرحلاً أو غير موجود.");
                        }

                        // حذف التفاصيل القديمة
                        string deleteDetailsSql = "DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId";
                        using (var deleteCommand = new SqlCommand(deleteDetailsSql, connection, transaction))
                        {
                            deleteCommand.Parameters.AddWithValue("@JournalEntryId", journalEntry.JournalEntryId);
                            deleteCommand.ExecuteNonQuery();
                        }

                        // إدراج التفاصيل الجديدة
                        if (journalEntry.JournalEntryDetails != null && journalEntry.JournalEntryDetails.Any())
                        {
                            string insertDetailsSql = @"
                                INSERT INTO JournalEntryDetails 
                                (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, 
                                 Reference, Description)
                                VALUES 
                                (@JournalEntryId, @AccountId, @LineNumber, @DebitAmount, @CreditAmount, 
                                 @Reference, @Description)";

                            foreach (var detail in journalEntry.JournalEntryDetails)
                            {
                                using (var detailCommand = new SqlCommand(insertDetailsSql, connection, transaction))
                                {
                                    detailCommand.Parameters.AddWithValue("@JournalEntryId", journalEntry.JournalEntryId);
                                    detailCommand.Parameters.AddWithValue("@AccountId", detail.AccountId);
                                    detailCommand.Parameters.AddWithValue("@LineNumber", detail.LineNumber);
                                    detailCommand.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                                    detailCommand.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                                    detailCommand.Parameters.AddWithValue("@Reference", detail.Reference ?? "");
                                    detailCommand.Parameters.AddWithValue("@Description", detail.Description ?? "");

                                    detailCommand.ExecuteNonQuery();
                                }
                            }
                        }

                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// حذف قيد يومي
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public bool DeleteJournalEntry(int journalEntryId)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حذف التفاصيل أولاً
                        string deleteDetailsSql = "DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId";
                        using (var deleteDetailsCommand = new SqlCommand(deleteDetailsSql, connection, transaction))
                        {
                            deleteDetailsCommand.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                            deleteDetailsCommand.ExecuteNonQuery();
                        }

                        // حذف القيد الرئيسي
                        string deleteJournalSql = @"
                            DELETE FROM JournalEntry
                            WHERE JournalEntryId = @JournalEntryId AND Status = @DraftStatus";

                        using (var deleteJournalCommand = new SqlCommand(deleteJournalSql, connection, transaction))
                        {
                            deleteJournalCommand.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                            deleteJournalCommand.Parameters.AddWithValue("@DraftStatus", (int)JournalStatus.Draft);

                            int rowsAffected = deleteJournalCommand.ExecuteNonQuery();
                            if (rowsAffected == 0)
                                throw new InvalidOperationException("لا يمكن حذف القيد. قد يكون مرحلاً أو غير موجود.");
                        }

                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// ترحيل قيد يومي
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>true إذا تم الترحيل بنجاح</returns>
        public bool PostJournalEntry(int journalEntryId)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // تحديث حالة القيد إلى مرحل
                        string updateStatusSql = @"
                            UPDATE JournalEntry
                            SET Status = @PostedStatus,
                                PostedDate = @PostedDate,
                                PostedBy = @PostedBy
                            WHERE JournalEntryId = @JournalEntryId AND Status = @DraftStatus";

                        using (var command = new SqlCommand(updateStatusSql, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                            command.Parameters.AddWithValue("@PostedStatus", (int)JournalStatus.Posted);
                            command.Parameters.AddWithValue("@PostedDate", DateTime.Now);
                            command.Parameters.AddWithValue("@PostedBy", "System"); // TODO: استخدام المستخدم الحالي
                            command.Parameters.AddWithValue("@DraftStatus", (int)JournalStatus.Draft);

                            int rowsAffected = command.ExecuteNonQuery();
                            if (rowsAffected == 0)
                                throw new InvalidOperationException("لا يمكن ترحيل القيد. قد يكون مرحلاً مسبقاً أو غير موجود.");
                        }

                        // TODO: إضافة منطق ترحيل القيد إلى دفتر الأستاذ العام
                        // هذا يتطلب تحديث أرصدة الحسابات

                        transaction.Commit();
                        return true;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
        #endregion

        #region Journal Entry Retrieval

        /// <summary>
        /// الحصول على قيد يومي بالمعرف
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>بيانات القيد</returns>
        public JournalEntry GetJournalEntryById(int journalEntryId)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();

                // الحصول على القيد الرئيسي
                string selectJournalSql = @"
                    SELECT JournalEntryId, JournalNumber, JournalDate, JournalType, Status,
                           GeneralDescription, TotalDebit, TotalCredit, CostCenterId,
                           CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, PostedDate, PostedBy
                    FROM JournalEntry
                    WHERE JournalEntryId = @JournalEntryId";

                JournalEntry journalEntry = null;
                using (var command = new SqlCommand(selectJournalSql, connection))
                {
                    command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            journalEntry = MapJournalEntryFromReader(reader);
                        }
                    }
                }

                if (journalEntry != null)
                {
                    // الحصول على تفاصيل القيد
                    journalEntry.JournalEntryDetails = GetJournalEntryDetails(journalEntryId, connection);
                }

                return journalEntry;
            }
        }

        /// <summary>
        /// الحصول على جميع القيود اليومية
        /// </summary>
        /// <returns>قائمة القيود</returns>
        public List<JournalEntry> GetAllJournalEntries()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();

                string selectSql = @"
                    SELECT JournalEntryId, JournalNumber, JournalDate, JournalType, Status,
                           GeneralDescription, TotalDebit, TotalCredit, CostCenterId,
                           CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, PostedDate, PostedBy
                    FROM JournalEntry
                    ORDER BY JournalDate DESC, JournalEntryId DESC";

                var journalEntries = new List<JournalEntry>();
                using (var command = new SqlCommand(selectSql, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var journalEntry = MapJournalEntryFromReader(reader);
                            journalEntries.Add(journalEntry);
                        }
                    }
                }

                // تحميل التفاصيل لكل قيد
                foreach (var journalEntry in journalEntries)
                {
                    journalEntry.JournalEntryDetails = GetJournalEntryDetails(journalEntry.JournalEntryId, connection);
                }

                return journalEntries;
            }
        }

        /// <summary>
        /// البحث في القيود اليومية
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة القيود المطابقة</returns>
        public List<JournalEntry> SearchJournalEntries(JournalEntrySearchCriteria searchCriteria)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                connection.Open();

                var whereConditions = new List<string>();
                var parameters = new List<SqlParameter>();

                // بناء شروط البحث
                if (!string.IsNullOrWhiteSpace(searchCriteria.JournalNumber))
                {
                    whereConditions.Add("JournalNumber LIKE @JournalNumber");
                    parameters.Add(new SqlParameter("@JournalNumber", $"%{searchCriteria.JournalNumber}%"));
                }

                if (searchCriteria.FromDate.HasValue)
                {
                    whereConditions.Add("JournalDate >= @FromDate");
                    parameters.Add(new SqlParameter("@FromDate", searchCriteria.FromDate.Value));
                }

                if (searchCriteria.ToDate.HasValue)
                {
                    whereConditions.Add("JournalDate <= @ToDate");
                    parameters.Add(new SqlParameter("@ToDate", searchCriteria.ToDate.Value));
                }

                if (searchCriteria.JournalType.HasValue)
                {
                    whereConditions.Add("JournalType = @JournalType");
                    parameters.Add(new SqlParameter("@JournalType", (int)searchCriteria.JournalType.Value));
                }

                if (searchCriteria.Status.HasValue)
                {
                    whereConditions.Add("Status = @Status");
                    parameters.Add(new SqlParameter("@Status", (int)searchCriteria.Status.Value));
                }

                if (searchCriteria.CostCenterId.HasValue)
                {
                    whereConditions.Add("CostCenterId = @CostCenterId");
                    parameters.Add(new SqlParameter("@CostCenterId", searchCriteria.CostCenterId.Value));
                }

                if (!string.IsNullOrWhiteSpace(searchCriteria.Description))
                {
                    whereConditions.Add("GeneralDescription LIKE @Description");
                    parameters.Add(new SqlParameter("@Description", $"%{searchCriteria.Description}%"));
                }

                string whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";

                string selectSql = $@"
                    SELECT JournalEntryId, JournalNumber, JournalDate, JournalType, Status,
                           GeneralDescription, TotalDebit, TotalCredit, CostCenterId,
                           CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, PostedDate, PostedBy
                    FROM JournalEntry
                    {whereClause}
                    ORDER BY JournalDate DESC, JournalEntryId DESC";

                var journalEntries = new List<JournalEntry>();
                using (var command = new SqlCommand(selectSql, connection))
                {
                    command.Parameters.AddRange(parameters.ToArray());
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var journalEntry = MapJournalEntryFromReader(reader);
                            journalEntries.Add(journalEntry);
                        }
                    }
                }

                // تحميل التفاصيل لكل قيد
                foreach (var journalEntry in journalEntries)
                {
                    journalEntry.JournalEntryDetails = GetJournalEntryDetails(journalEntry.JournalEntryId, connection);
                }

                return journalEntries;
            }
        }
        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل بيانات القارئ إلى كائن قيد يومي
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن القيد اليومي</returns>
        private JournalEntry MapJournalEntryFromReader(SqlDataReader reader)
        {
            return new JournalEntry
            {
                JournalEntryId = reader.GetInt32("JournalEntryId"),
                JournalNumber = reader.GetString("JournalNumber"),
                JournalDate = reader.GetDateTime("JournalDate"),
                JournalType = (JournalType)reader.GetInt32("JournalType"),
                Status = (JournalStatus)reader.GetInt32("Status"),
                GeneralDescription = reader.IsDBNull("GeneralDescription") ? "" : reader.GetString("GeneralDescription"),
                TotalDebit = reader.GetDecimal("TotalDebit"),
                TotalCredit = reader.GetDecimal("TotalCredit"),
                CostCenterId = reader.IsDBNull("CostCenterId") ? null : reader.GetInt32("CostCenterId"),
                CreatedDate = reader.GetDateTime("CreatedDate"),
                CreatedBy = reader.IsDBNull("CreatedBy") ? 0 : (int.TryParse(reader.GetString("CreatedBy"), out int createdBy) ? createdBy : 0),
                ModifiedDate = reader.IsDBNull("ModifiedDate") ? null : reader.GetDateTime("ModifiedDate"),
                ModifiedBy = reader.IsDBNull("ModifiedBy") ? null : (int.TryParse(reader.GetString("ModifiedBy"), out int modifiedBy) ? modifiedBy : (int?)null),
                PostedDate = reader.IsDBNull("PostedDate") ? null : reader.GetDateTime("PostedDate"),
                PostedBy = reader.IsDBNull("PostedBy") ? null : (int.TryParse(reader.GetString("PostedBy"), out int postedBy) ? postedBy : (int?)null),
                JournalEntryDetails = new List<JournalEntryDetail>()
            };
        }

        /// <summary>
        /// الحصول على تفاصيل القيد
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <param name="connection">اتصال قاعدة البيانات</param>
        /// <returns>قائمة تفاصيل القيد</returns>
        private List<JournalEntryDetail> GetJournalEntryDetails(int journalEntryId, SqlConnection connection)
        {
            string selectDetailsSql = @"
                SELECT jed.JournalEntryDetailId, jed.JournalEntryId, jed.AccountId, jed.LineNumber,
                       jed.DebitAmount, jed.CreditAmount, jed.Reference, jed.Description,
                       coa.AccountCode, coa.AccountNameAr, coa.AccountNameEn
                FROM JournalEntryDetails jed
                INNER JOIN ChartOfAccounts coa ON jed.AccountId = coa.AccountId
                WHERE jed.JournalEntryId = @JournalEntryId
                ORDER BY jed.LineNumber";

            var details = new List<JournalEntryDetail>();
            using (var command = new SqlCommand(selectDetailsSql, connection))
            {
                command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var detail = new JournalEntryDetail
                        {
                            JournalEntryDetailId = reader.GetInt32("JournalEntryDetailId"),
                            JournalEntryId = reader.GetInt32("JournalEntryId"),
                            AccountId = reader.GetInt32("AccountId"),
                            LineNumber = reader.GetInt32("LineNumber"),
                            DebitAmount = reader.GetDecimal("DebitAmount"),
                            CreditAmount = reader.GetDecimal("CreditAmount"),
                            Reference = reader.IsDBNull("Reference") ? "" : reader.GetString("Reference"),
                            Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description"),
                            Account = new ChartOfAccount
                            {
                                AccountId = reader.GetInt32("AccountId"),
                                AccountCode = reader.GetString("AccountCode"),
                                AccountNameAr = reader.GetString("AccountNameAr"),
                                AccountNameEn = reader.IsDBNull("AccountNameEn") ? "" : reader.GetString("AccountNameEn")
                            }
                        };
                        details.Add(detail);
                    }
                }
            }

            return details;
        }
        #endregion
    }
}
