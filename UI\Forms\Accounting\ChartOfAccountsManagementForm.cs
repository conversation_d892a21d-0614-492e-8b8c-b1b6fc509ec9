using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.Common.Helpers;
using UIHelper = Awqaf_Managment.UI.Helpers.UIHelper;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    public partial class ChartOfAccountsManagementForm : Form
    {
        #region Fields
        private List<ChartOfAccount> _accounts;
        private List<AccountType> _accountTypes;
        private List<AccountGroup> _accountGroups;
        private List<Currency> _currencies;
        private ChartOfAccount _selectedAccount;
        private bool _isEditMode = false;
        private bool _isDarkMode = false;
        #endregion

        #region Constructor
        public ChartOfAccountsManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }
        #endregion

        #region Initialization
        private void InitializeForm()
        {
            // إعداد النموذج للغة العربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            
            // تطبيق الخطوط العربية
            UIHelper.ApplyArabicFont(this);
            
            // تطبيق التصميم العصري
            ApplyModernDesign();
            
            // إعداد الأحداث
            SetupEvents();
            
            // تحميل البيانات
            LoadData();
            
            // إعداد شجرة الحسابات
            SetupAccountsTree();
            
            // إعداد النموذج
            SetupForm();
            
            // تطبيق الوضع الافتراضي
            SetAddMode();
        }

        private void ApplyModernDesign()
        {
            // ألوان عصرية
            this.BackColor = Color.FromArgb(248, 249, 250);
            
            // تطبيق التصميم على اللوحات
            foreach (Control control in this.Controls)
            {
                if (control is Panel panel)
                {
                    panel.BackColor = Color.White;
                    panel.BorderStyle = BorderStyle.None;
                    
                    // إضافة ظل خفيف
                    AddShadowEffect(panel);
                }
                else if (control is GroupBox groupBox)
                {
                    groupBox.BackColor = Color.White;
                    groupBox.ForeColor = Color.FromArgb(52, 58, 64);
                    groupBox.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
                }
            }
            
            // تطبيق التصميم على الأزرار
            ApplyButtonStyles();
        }

        private void AddShadowEffect(Panel panel)
        {
            // محاكاة تأثير الظل بحدود ملونة
            panel.Paint += (s, e) =>
            {
                var rect = panel.ClientRectangle;
                rect.Width -= 1;
                rect.Height -= 1;
                
                using (var pen = new Pen(Color.FromArgb(30, 0, 0, 0), 1))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };
        }

        private void ApplyButtonStyles()
        {
            // أزرار ملونة حسب الوظيفة
            if (btnAdd != null)
            {
                StyleButton(btnAdd, Color.FromArgb(40, 167, 69), Color.White, "➕");
            }
            
            if (btnEdit != null)
            {
                StyleButton(btnEdit, Color.FromArgb(0, 123, 255), Color.White, "✏️");
            }
            
            if (btnDelete != null)
            {
                StyleButton(btnDelete, Color.FromArgb(220, 53, 69), Color.White, "🗑️");
            }
            
            if (btnSave != null)
            {
                StyleButton(btnSave, Color.FromArgb(23, 162, 184), Color.White, "💾");
            }
            
            if (btnCancel != null)
            {
                StyleButton(btnCancel, Color.FromArgb(108, 117, 125), Color.White, "❌");
            }
            
            if (btnRefresh != null)
            {
                StyleButton(btnRefresh, Color.FromArgb(111, 66, 193), Color.White, "🔄");
            }
        }

        private void StyleButton(Button button, Color backColor, Color foreColor, string icon)
        {
            button.BackColor = backColor;
            button.ForeColor = foreColor;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backColor, 0.1f);
            button.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            button.Text = $"{icon} {button.Text}";
            button.Cursor = Cursors.Hand;
            
            // تأثير الحواف المدورة
            button.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, button.Width, button.Height, 8, 8));
        }

        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(int nLeftRect, int nTopRect, int nRightRect, int nBottomRect, int nWidthEllipse, int nHeightEllipse);

        private void SetupEvents()
        {
            // أحداث الأزرار
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnToggleDarkMode.Click += BtnToggleDarkMode_Click;
            
            // أحداث شجرة الحسابات
            treeAccounts.AfterSelect += TreeAccounts_AfterSelect;
            treeAccounts.NodeMouseDoubleClick += TreeAccounts_NodeMouseDoubleClick;
            
            // أحداث البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            
            // أحداث ComboBox
            cmbAccountType.SelectedIndexChanged += CmbAccountType_SelectedIndexChanged;
            cmbParentAccount.SelectedIndexChanged += CmbParentAccount_SelectedIndexChanged;
            
            // أحداث النموذج
            this.FormClosing += ChartOfAccountsManagementForm_FormClosing;
            
            // أحداث التحقق
            txtAccountCode.Leave += TxtAccountCode_Leave;
            txtAccountNameAr.Leave += TxtAccountNameAr_Leave;
        }
        #endregion

        #region Data Loading
        private void LoadData()
        {
            try
            {
                // تحميل الحسابات
                _accounts = ChartOfAccountsService.GetAllAccounts() ?? new List<ChartOfAccount>();
                
                // تحميل أنواع الحسابات
                LoadAccountTypes();
                
                // تحميل مجموعات الحسابات
                LoadAccountGroups();
                
                // تحميل العملات
                LoadCurrencies();
                
                // تحديث شجرة الحسابات
                RefreshAccountsTree();

                // تحديث القوائم المنسدلة
                LoadComboBoxes();
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        private void LoadAccountTypes()
        {
            _accountTypes = new List<AccountType>
            {
                new AccountType { AccountTypeId = 1, TypeNameAr = "أصول", TypeNameEn = "Assets", DefaultNature = AccountNature.Debit, AccountTypeName = "أصول" },
                new AccountType { AccountTypeId = 2, TypeNameAr = "خصوم", TypeNameEn = "Liabilities", DefaultNature = AccountNature.Credit, AccountTypeName = "خصوم" },
                new AccountType { AccountTypeId = 3, TypeNameAr = "حقوق ملكية", TypeNameEn = "Equity", DefaultNature = AccountNature.Credit, AccountTypeName = "حقوق ملكية" },
                new AccountType { AccountTypeId = 4, TypeNameAr = "إيرادات", TypeNameEn = "Revenue", DefaultNature = AccountNature.Credit, AccountTypeName = "إيرادات" },
                new AccountType { AccountTypeId = 5, TypeNameAr = "مصروفات", TypeNameEn = "Expenses", DefaultNature = AccountNature.Debit, AccountTypeName = "مصروفات" },
                new AccountType { AccountTypeId = 6, TypeNameAr = "حسابات نظامية", TypeNameEn = "System Accounts", DefaultNature = AccountNature.Debit, AccountTypeName = "حسابات نظامية" }
            };

            cmbAccountType.DataSource = _accountTypes;
            cmbAccountType.DisplayMember = "AccountTypeName";
            cmbAccountType.ValueMember = "AccountTypeId";
            cmbAccountType.SelectedIndex = -1;
        }

        private void LoadAccountGroups()
        {
            _accountGroups = new List<AccountGroup>
            {
                // مجموعات الأصول
                new AccountGroup { AccountGroupId = 1, GroupNameAr = "أصول متداولة", AccountTypeId = 1, GroupName = "أصول متداولة" },
                new AccountGroup { AccountGroupId = 2, GroupNameAr = "أصول ثابتة", AccountTypeId = 1, GroupName = "أصول ثابتة" },
                new AccountGroup { AccountGroupId = 3, GroupNameAr = "أصول غير ملموسة", AccountTypeId = 1, GroupName = "أصول غير ملموسة" },

                // مجموعات الخصوم
                new AccountGroup { AccountGroupId = 4, GroupNameAr = "خصوم متداولة", AccountTypeId = 2, GroupName = "خصوم متداولة" },
                new AccountGroup { AccountGroupId = 5, GroupNameAr = "خصوم طويلة الأجل", AccountTypeId = 2, GroupName = "خصوم طويلة الأجل" },

                // مجموعات حقوق الملكية
                new AccountGroup { AccountGroupId = 6, GroupNameAr = "رأس المال", AccountTypeId = 3, GroupName = "رأس المال" },
                new AccountGroup { AccountGroupId = 7, GroupNameAr = "الأرباح المحتجزة", AccountTypeId = 3, GroupName = "الأرباح المحتجزة" },

                // مجموعات الإيرادات
                new AccountGroup { AccountGroupId = 8, GroupNameAr = "إيرادات تشغيلية", AccountTypeId = 4, GroupName = "إيرادات تشغيلية" },
                new AccountGroup { AccountGroupId = 9, GroupNameAr = "إيرادات أخرى", AccountTypeId = 4, GroupName = "إيرادات أخرى" },

                // مجموعات المصروفات
                new AccountGroup { AccountGroupId = 10, GroupNameAr = "مصروفات تشغيلية", AccountTypeId = 5, GroupName = "مصروفات تشغيلية" },
                new AccountGroup { AccountGroupId = 11, GroupNameAr = "مصروفات إدارية", AccountTypeId = 5, GroupName = "مصروفات إدارية" },
                new AccountGroup { AccountGroupId = 12, GroupNameAr = "مصروفات أخرى", AccountTypeId = 5, GroupName = "مصروفات أخرى" }
            };
        }

        private void LoadCurrencies()
        {
            _currencies = new List<Currency>
            {
                new Currency { CurrencyId = 1, CurrencyCode = "SAR", CurrencyNameAr = "ريال سعودي", Symbol = "ر.س" },
                new Currency { CurrencyId = 2, CurrencyCode = "USD", CurrencyNameAr = "دولار أمريكي", Symbol = "$" },
                new Currency { CurrencyId = 3, CurrencyCode = "EUR", CurrencyNameAr = "يورو", Symbol = "€" }
            };

            cmbCurrency.DataSource = _currencies;
            cmbCurrency.DisplayMember = "CurrencyNameAr";
            cmbCurrency.ValueMember = "CurrencyId";
            cmbCurrency.SelectedIndex = 0; // افتراضي: ريال سعودي
        }

        private void LoadComboBoxes()
        {
            // تحديث قائمة الحسابات الأب
            LoadParentAccounts();

            // تحديث قائمة مجموعات الحسابات حسب النوع المحدد
            if (cmbAccountType.SelectedValue != null)
            {
                LoadAccountGroupsByType((int)cmbAccountType.SelectedValue);
            }
        }

        private void LoadParentAccounts()
        {
            var parentAccounts = _accounts.Where(a => a.LevelType == AccountLevelType.Main || a.LevelType == AccountLevelType.Sub).ToList();
            parentAccounts.Insert(0, new ChartOfAccount { AccountId = 0, AccountNameAr = "-- لا يوجد حساب أب --" });

            cmbParentAccount.DataSource = parentAccounts;
            cmbParentAccount.DisplayMember = "AccountNameAr";
            cmbParentAccount.ValueMember = "AccountId";
            cmbParentAccount.SelectedIndex = 0;
        }

        private void LoadAccountGroupsByType(int accountTypeId)
        {
            var groups = _accountGroups.Where(g => g.AccountTypeId == accountTypeId).ToList();

            cmbAccountGroup.DataSource = groups;
            cmbAccountGroup.DisplayMember = "GroupNameAr";
            cmbAccountGroup.ValueMember = "AccountGroupId";
            cmbAccountGroup.SelectedIndex = -1;
        }
        #endregion

        #region Tree Management
        private void SetupAccountsTree()
        {
            treeAccounts.ImageList = CreateTreeImageList();
            treeAccounts.ShowLines = true;
            treeAccounts.ShowPlusMinus = true;
            treeAccounts.ShowRootLines = true;
            treeAccounts.HideSelection = false;
            treeAccounts.FullRowSelect = true;
            treeAccounts.BackColor = Color.White;
            treeAccounts.ForeColor = Color.FromArgb(52, 58, 64);
            treeAccounts.Font = new Font("Segoe UI", 10F);
        }

        private ImageList CreateTreeImageList()
        {
            var imageList = new ImageList();
            imageList.ImageSize = new Size(16, 16);

            // إضافة أيقونات للحسابات
            var folderIcon = SystemIcons.Application.ToBitmap();
            var accountIcon = SystemIcons.Information.ToBitmap();

            imageList.Images.Add("folder", folderIcon);
            imageList.Images.Add("account", accountIcon);

            return imageList;
        }

        private void RefreshAccountsTree()
        {
            treeAccounts.Nodes.Clear();

            // إنشاء العقد الجذرية (الحسابات الرئيسية)
            var rootAccounts = _accounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0).OrderBy(a => a.AccountCode);

            foreach (var account in rootAccounts)
            {
                var node = CreateAccountNode(account);
                treeAccounts.Nodes.Add(node);
                AddChildNodes(node, account.AccountId);
            }

            treeAccounts.ExpandAll();
        }

        private TreeNode CreateAccountNode(ChartOfAccount account)
        {
            var node = new TreeNode($"{account.AccountCode} - {account.AccountNameAr}")
            {
                Tag = account,
                ImageKey = account.IsParentAccount ? "folder" : "account",
                SelectedImageKey = account.IsParentAccount ? "folder" : "account"
            };

            // تلوين العقد حسب نوع الحساب
            switch (account.AccountTypeId)
            {
                case 1: // أصول
                    node.ForeColor = Color.FromArgb(40, 167, 69);
                    break;
                case 2: // خصوم
                    node.ForeColor = Color.FromArgb(220, 53, 69);
                    break;
                case 3: // حقوق ملكية
                    node.ForeColor = Color.FromArgb(111, 66, 193);
                    break;
                case 4: // إيرادات
                    node.ForeColor = Color.FromArgb(0, 123, 255);
                    break;
                case 5: // مصروفات
                    node.ForeColor = Color.FromArgb(255, 193, 7);
                    break;
                default:
                    node.ForeColor = Color.FromArgb(108, 117, 125);
                    break;
            }

            return node;
        }

        private void AddChildNodes(TreeNode parentNode, int parentAccountId)
        {
            var childAccounts = _accounts.Where(a => a.ParentAccountId == parentAccountId).OrderBy(a => a.AccountCode);

            foreach (var account in childAccounts)
            {
                var childNode = CreateAccountNode(account);
                parentNode.Nodes.Add(childNode);
                AddChildNodes(childNode, account.AccountId);
            }
        }
        #endregion

        #region Form Management
        private void SetupForm()
        {
            // إعداد الحقول
            txtAccountCode.ReadOnly = true; // يُولّد تلقائياً
            txtAccountLevel.ReadOnly = true; // محسوب تلقائياً

            // إعداد القيم الافتراضية
            chkIsActive.Checked = true;
            chkAllowPosting.Checked = true;
            chkAllowDirectEntry.Checked = true;
            chkIsParentAccount.Checked = false;

            // إعداد نوع الرصيد
            cmbBalanceType.Items.AddRange(new string[] { "مدين", "دائن" });
            cmbBalanceType.SelectedIndex = 0;
        }

        private void SetAddMode()
        {
            _isEditMode = false;
            _selectedAccount = null;

            ClearForm();
            EnableFormControls(true);

            btnAdd.Enabled = false;
            btnEdit.Enabled = false;
            btnDelete.Enabled = false;
            btnSave.Enabled = true;
            btnCancel.Enabled = true;

            txtAccountNameAr.Focus();
        }

        private void SetEditMode(ChartOfAccount account)
        {
            _isEditMode = true;
            _selectedAccount = account;

            LoadAccountToForm(account);
            EnableFormControls(true);

            btnAdd.Enabled = false;
            btnEdit.Enabled = false;
            btnDelete.Enabled = false;
            btnSave.Enabled = true;
            btnCancel.Enabled = true;
        }

        private void SetViewMode()
        {
            _isEditMode = false;

            EnableFormControls(false);

            btnAdd.Enabled = true;
            btnEdit.Enabled = _selectedAccount != null;
            btnDelete.Enabled = _selectedAccount != null && !_selectedAccount.IsParentAccount;
            btnSave.Enabled = false;
            btnCancel.Enabled = false;
        }

        private void EnableFormControls(bool enabled)
        {
            foreach (Control control in pnlAccountData.Controls)
            {
                if (control is TextBox || control is ComboBox || control is CheckBox)
                {
                    control.Enabled = enabled;
                }
            }

            foreach (Control control in pnlSettings.Controls)
            {
                if (control is CheckBox || control is ComboBox)
                {
                    control.Enabled = enabled;
                }
            }

            foreach (Control control in pnlOpeningBalance.Controls)
            {
                if (control is TextBox || control is ComboBox)
                {
                    control.Enabled = enabled;
                }
            }

            // الحقول التي تبقى للقراءة فقط
            txtAccountCode.ReadOnly = true;
            txtAccountLevel.ReadOnly = true;
        }

        private void ClearForm()
        {
            txtAccountCode.Clear();
            txtAccountNameAr.Clear();
            txtAccountNameEn.Clear();
            txtAccountLevel.Clear();
            txtOpeningBalance.Clear();
            txtDescription.Clear();

            cmbParentAccount.SelectedIndex = -1;
            cmbAccountType.SelectedIndex = -1;
            cmbAccountGroup.SelectedIndex = -1;
            cmbCurrency.SelectedIndex = 0;
            cmbBalanceType.SelectedIndex = 0;

            chkIsActive.Checked = true;
            chkAllowPosting.Checked = true;
            chkAllowDirectEntry.Checked = true;
            chkIsParentAccount.Checked = false;
        }

        private void LoadAccountToForm(ChartOfAccount account)
        {
            txtAccountCode.Text = account.AccountCode;
            txtAccountNameAr.Text = account.AccountNameAr;
            txtAccountNameEn.Text = account.AccountNameEn ?? "";
            txtAccountLevel.Text = account.AccountLevel.ToString();
            txtOpeningBalance.Text = account.OpeningBalance.ToString("F2");
            txtDescription.Text = account.Description ?? "";

            // تحديد الحساب الأب
            if (account.ParentAccountId.HasValue)
            {
                var parentAccount = _accounts.FirstOrDefault(a => a.AccountId == account.ParentAccountId.Value);
                if (parentAccount != null)
                {
                    cmbParentAccount.SelectedValue = parentAccount.AccountId;
                }
            }

            cmbAccountType.SelectedValue = account.AccountTypeId;
            cmbAccountGroup.SelectedValue = account.AccountGroupId;
            cmbCurrency.SelectedValue = account.CurrencyId ?? 1;
            cmbBalanceType.SelectedItem = account.Nature;

            chkIsActive.Checked = account.IsActive;
            chkAllowPosting.Checked = account.AllowPosting;
            chkAllowDirectEntry.Checked = account.AllowDirectEntry;
            chkIsParentAccount.Checked = account.IsParentAccount;
        }
        #endregion

        #region Event Handlers
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            SetAddMode();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedAccount != null)
            {
                SetEditMode(_selectedAccount);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedAccount == null) return;

            if (_selectedAccount.IsParentAccount)
            {
                UIHelper.ShowWarningMessage("لا يمكن حذف حساب أب يحتوي على حسابات فرعية");
                return;
            }

            var result = UIHelper.ShowConfirmMessage($"هل تريد حذف الحساب '{_selectedAccount.AccountNameAr}'؟", "تأكيد الحذف");

            if (result == DialogResult.Yes)
            {
                try
                {
                    ChartOfAccountsService.DeleteAccount(_selectedAccount.AccountId);
                    LoadData();
                    SetViewMode();

                    UIHelper.ShowInfoMessage("تم حذف الحساب بنجاح");
                }
                catch (Exception ex)
                {
                    UIHelper.ShowErrorMessage($"خطأ في حذف الحساب: {ex.Message}");
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                var account = CreateAccountFromForm();

                if (_isEditMode)
                {
                    account.AccountId = _selectedAccount.AccountId;
                    ChartOfAccountsService.UpdateAccount(account);
                    MessageBox.Show("تم تحديث الحساب بنجاح", "نجح التحديث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
                else
                {
                    ChartOfAccountsService.AddAccount(account);
                    MessageBox.Show("تم إضافة الحساب بنجاح", "نجحت الإضافة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }

                LoadData();
                SetViewMode();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            SetViewMode();
            if (_selectedAccount != null)
            {
                LoadAccountToForm(_selectedAccount);
            }
            else
            {
                ClearForm();
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void BtnToggleDarkMode_Click(object sender, EventArgs e)
        {
            _isDarkMode = !_isDarkMode;
            ApplyTheme();
        }

        private void TreeAccounts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is ChartOfAccount account)
            {
                _selectedAccount = account;
                LoadAccountToForm(account);
                SetViewMode();
            }
        }

        private void TreeAccounts_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node?.Tag is ChartOfAccount account)
            {
                _selectedAccount = account;
                SetEditMode(account);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterAccountsTree(txtSearch.Text);
        }

        private void CmbAccountType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbAccountType.SelectedValue != null)
            {
                var accountTypeId = (int)cmbAccountType.SelectedValue;
                LoadAccountGroupsByType(accountTypeId);

                // تحديد طبيعة الحساب تلقائياً
                var accountType = _accountTypes.FirstOrDefault(at => at.AccountTypeId == accountTypeId);
                if (accountType != null)
                {
                    cmbBalanceType.SelectedItem = accountType.Nature;
                }
            }
        }

        private void CmbParentAccount_SelectedIndexChanged(object sender, EventArgs e)
        {
            GenerateAccountCode();
            CalculateAccountLevel();
        }

        private void TxtAccountCode_Leave(object sender, EventArgs e)
        {
            CheckAccountCodeDuplication();
            CalculateAccountLevel();
        }

        private void TxtAccountNameAr_Leave(object sender, EventArgs e)
        {
            CheckAccountNameDuplication();
        }

        private void ChartOfAccountsManagementForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (btnSave.Enabled)
            {
                var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
        #endregion

        #region Helper Methods


        private void FilterAccountsTree(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                RefreshAccountsTree();
                return;
            }

            treeAccounts.Nodes.Clear();

            var filteredAccounts = _accounts.Where(a =>
                a.AccountCode.Contains(searchText) ||
                a.AccountNameAr.Contains(searchText) ||
                (a.AccountNameEn != null && a.AccountNameEn.Contains(searchText))
            ).ToList();

            foreach (var account in filteredAccounts)
            {
                var node = CreateAccountNode(account);
                treeAccounts.Nodes.Add(node);
            }

            treeAccounts.ExpandAll();
        }

        private void GenerateAccountCode()
        {
            try
            {
                if (cmbParentAccount.SelectedValue == null)
                {
                    // حساب رئيسي
                    var existingCodes = _accounts.Select(a => a.AccountCode).ToList();
                    var newCode = AccountCodeGenerator.GenerateNextCode("", existingCodes);
                    txtAccountCode.Text = newCode;
                }
                else
                {
                    // حساب فرعي
                    var parentAccountId = (int)cmbParentAccount.SelectedValue;
                    var parentAccount = _accounts.FirstOrDefault(a => a.AccountId == parentAccountId);

                    if (parentAccount != null)
                    {
                        var existingCodes = _accounts.Select(a => a.AccountCode).ToList();
                        var newCode = AccountCodeGenerator.GenerateNextCode(parentAccount.AccountCode, existingCodes);
                        txtAccountCode.Text = newCode;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد كود الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void CalculateAccountLevel()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
                {
                    txtAccountLevel.Text = "1";
                    return;
                }

                var level = AccountCodeGenerator.GetAccountLevel(txtAccountCode.Text);
                txtAccountLevel.Text = level.ToString();
            }
            catch (Exception)
            {
                txtAccountLevel.Text = "1";
            }
        }

        private void CheckAccountCodeDuplication()
        {
            if (string.IsNullOrWhiteSpace(txtAccountCode.Text)) return;

            // التحقق من صحة تنسيق الكود
            if (!AccountCodeGenerator.ValidateAccountCode(txtAccountCode.Text))
            {
                MessageBox.Show("تنسيق رمز الحساب غير صحيح. يجب أن يكون بالشكل: X.XX.XXX", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountCode.Focus();
                return;
            }

            // التحقق من عدم التكرار
            var existingAccount = _accounts.FirstOrDefault(a =>
                a.AccountCode == txtAccountCode.Text &&
                (_selectedAccount == null || a.AccountId != _selectedAccount.AccountId));

            if (existingAccount != null)
            {
                MessageBox.Show("رمز الحساب موجود مسبقاً. يرجى اختيار رمز آخر.", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountCode.Focus();
            }
        }

        private void CheckAccountNameDuplication()
        {
            if (string.IsNullOrWhiteSpace(txtAccountNameAr.Text)) return;

            var existingAccount = _accounts.FirstOrDefault(a =>
                a.AccountNameAr == txtAccountNameAr.Text &&
                (_selectedAccount == null || a.AccountId != _selectedAccount.AccountId));

            if (existingAccount != null)
            {
                MessageBox.Show("اسم الحساب موجود مسبقاً. يرجى اختيار اسم آخر.", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountNameAr.Focus();
            }
        }

        private void ApplyTheme()
        {
            if (_isDarkMode)
            {
                // الوضع الليلي
                this.BackColor = Color.FromArgb(33, 37, 41);

                foreach (Control control in this.Controls)
                {
                    ApplyDarkThemeToControl(control);
                }
            }
            else
            {
                // الوضع النهاري
                this.BackColor = Color.FromArgb(248, 249, 250);
                ApplyModernDesign();
            }
        }

        private void ApplyDarkThemeToControl(Control control)
        {
            if (control is Panel || control is GroupBox)
            {
                control.BackColor = Color.FromArgb(52, 58, 64);
                control.ForeColor = Color.White;
            }
            else if (control is TextBox || control is ComboBox)
            {
                control.BackColor = Color.FromArgb(73, 80, 87);
                control.ForeColor = Color.White;
            }
            else if (control is TreeView)
            {
                control.BackColor = Color.FromArgb(52, 58, 64);
                control.ForeColor = Color.White;
            }

            foreach (Control child in control.Controls)
            {
                ApplyDarkThemeToControl(child);
            }
        }

        private bool ValidateForm()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtAccountNameAr.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب بالعربية", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountNameAr.Focus();
                return false;
            }

            if (cmbAccountType.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                cmbAccountType.Focus();
                return false;
            }

            if (cmbAccountGroup.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار مجموعة الحساب", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                cmbAccountGroup.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (!string.IsNullOrWhiteSpace(txtOpeningBalance.Text))
            {
                if (!decimal.TryParse(txtOpeningBalance.Text, out decimal balance))
                {
                    MessageBox.Show("يرجى إدخال رصيد افتتاحي صحيح", "قيمة غير صحيحة",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    txtOpeningBalance.Focus();
                    return false;
                }
            }

            return true;
        }

        private ChartOfAccount CreateAccountFromForm()
        {
            var account = new ChartOfAccount
            {
                AccountCode = txtAccountCode.Text,
                AccountNameAr = txtAccountNameAr.Text.Trim(),
                AccountNameEn = string.IsNullOrWhiteSpace(txtAccountNameEn.Text) ? null : txtAccountNameEn.Text.Trim(),
                AccountTypeId = (int)cmbAccountType.SelectedValue,
                AccountGroupId = (int)cmbAccountGroup.SelectedValue,
                ParentAccountId = cmbParentAccount.SelectedValue as int?,
                AccountLevel = int.Parse(txtAccountLevel.Text),
                Nature = (AccountNature)cmbBalanceType.SelectedValue,
                CurrencyId = (int)cmbCurrency.SelectedValue,
                OpeningBalance = string.IsNullOrWhiteSpace(txtOpeningBalance.Text) ? 0 : decimal.Parse(txtOpeningBalance.Text),
                IsActive = chkIsActive.Checked,
                AllowPosting = chkAllowPosting.Checked,
                AllowDirectEntry = chkAllowDirectEntry.Checked,
                IsParentAccount = chkIsParentAccount.Checked,
                Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim(),
                CreatedDate = DateTime.Now,
                CreatedBy = 1 // يجب استبداله بالمستخدم الحالي
            };

            return account;
        }


        #endregion
    }
}
