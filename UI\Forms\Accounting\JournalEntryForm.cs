using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlClient;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.DataAccess;
using UIHelper = Awqaf_Managment.UI.Helpers.UIHelper;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    public partial class JournalEntryForm : Form
    {
        #region Fields
        private JournalEntry _currentJournalEntry;
        private List<ChartOfAccount> _accounts;
        private List<CostCenter> _costCenters;
        private bool _isAddMode = false;
        private bool _isEditMode = false;
        private bool _isDirty = false;
        #endregion

        #region Constructor
        public JournalEntryForm()
        {
            InitializeComponent();
            InitializeForm();
        }
        #endregion

        #region Initialization
        private void InitializeForm()
        {
            // إعداد النموذج للغة العربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            
            // تطبيق الخطوط العربية
            UIHelper.ApplyArabicFont(this);
            
            // إعداد الأحداث
            SetupEvents();
            
            // تحميل البيانات
            LoadData();
            
            // إعداد DataGridView
            SetupDataGridView();
            
            // إعداد الأزرار
            SetupButtons();
            
            // بدء وضع الإضافة
            StartAddMode();
        }

        private void SetupEvents()
        {
            // أحداث الأزرار
            btnNew.Click += BtnNew_Click;
            btnSave.Click += BtnSave_Click;
            btnPost.Click += BtnPost_Click;
            btnDelete.Click += BtnDelete_Click;
            btnPrint.Click += BtnPrint_Click;
            btnExit.Click += BtnExit_Click;
            
            // أحداث DataGridView
            dgvJournalDetails.CellValueChanged += DgvJournalDetails_CellValueChanged;
            dgvJournalDetails.CellValidating += DgvJournalDetails_CellValidating;
            dgvJournalDetails.KeyDown += DgvJournalDetails_KeyDown;
            dgvJournalDetails.UserAddedRow += DgvJournalDetails_UserAddedRow;
            dgvJournalDetails.UserDeletingRow += DgvJournalDetails_UserDeletingRow;
            
            // أحداث النموذج
            this.FormClosing += JournalEntryForm_FormClosing;
            
            // أحداث التحكم في التاريخ
            dtpJournalDate.ValueChanged += DtpJournalDate_ValueChanged;
            
            // أحداث نوع القيد
            cmbJournalType.SelectedIndexChanged += CmbJournalType_SelectedIndexChanged;
        }

        private void LoadData()
        {
            try
            {
                // تحميل الحسابات
                _accounts = ChartOfAccountsService.GetAllAccounts() ?? new List<ChartOfAccount>();
                
                // تحميل مراكز التكلفة
                _costCenters = LoadCostCentersFromDatabase();
                
                // تحميل أنواع القيود
                LoadJournalTypes();
                
                // تحميل مراكز التكلفة في ComboBox
                LoadCostCenters();
                
                System.Diagnostics.Debug.WriteLine($"تم تحميل {_accounts.Count} حساب محاسبي");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void LoadJournalTypes()
        {
            var journalTypes = new[]
            {
                new { Value = JournalType.General, Text = "عادي" },
                new { Value = JournalType.Recurring, Text = "دوري" },
                new { Value = JournalType.Reversing, Text = "عكسي" },
                new { Value = JournalType.Closing, Text = "إقفال" },
                new { Value = JournalType.Adjusting, Text = "تسوية" }
            };
            
            cmbJournalType.DataSource = journalTypes;
            cmbJournalType.DisplayMember = "Text";
            cmbJournalType.ValueMember = "Value";
            cmbJournalType.SelectedIndex = 0; // افتراضي: عادي
        }

        private List<CostCenter> LoadCostCentersFromDatabase()
        {
            try
            {
                string connectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;Charset=UTF8;";
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    string query = @"
                        SELECT CostCenterId, CostCenterCode, CostCenterName, CostCenterNameAr,
                               Description, IsActive
                        FROM CostCenters
                        WHERE IsActive = 1
                        ORDER BY CostCenterCode";

                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        var costCenters = new List<CostCenter>();
                        while (reader.Read())
                        {
                            costCenters.Add(new CostCenter
                            {
                                CostCenterId = reader.GetInt32("CostCenterId"),
                                CostCenterCode = reader.GetString("CostCenterCode"),
                                CostCenterName = reader.IsDBNull("CostCenterName") ? "" : reader.GetString("CostCenterName"),
                                CostCenterNameAr = reader.IsDBNull("CostCenterNameAr") ? "" : reader.GetString("CostCenterNameAr"),
                                Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description"),
                                IsActive = reader.GetBoolean("IsActive")
                            });
                        }
                        return costCenters;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل مراكز التكلفة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                // إرجاع قائمة افتراضية في حالة الخطأ
                return new List<CostCenter>
                {
                    new CostCenter { CostCenterId = 1, CostCenterCode = "CC001", CostCenterNameAr = "الإدارة العامة" },
                    new CostCenter { CostCenterId = 2, CostCenterCode = "CC002", CostCenterNameAr = "المحاسبة والمالية" }
                };
            }
        }

        private void LoadCostCenters()
        {
            var costCenters = _costCenters.ToList();
            costCenters.Insert(0, new CostCenter { CostCenterId = 0, CostCenterNameAr = "-- لا يوجد --" });

            cmbCostCenter.DataSource = costCenters;
            cmbCostCenter.DisplayMember = "CostCenterNameAr";
            cmbCostCenter.ValueMember = "CostCenterId";
            cmbCostCenter.SelectedIndex = 0;
        }
        #endregion

        #region DataGridView Setup
        private void SetupDataGridView()
        {
            dgvJournalDetails.AllowUserToAddRows = true;
            dgvJournalDetails.AllowUserToDeleteRows = true;
            dgvJournalDetails.MultiSelect = false;
            dgvJournalDetails.SelectionMode = DataGridViewSelectionMode.CellSelect;
            dgvJournalDetails.EditMode = DataGridViewEditMode.EditOnEnter;
            
            // إعداد الأعمدة
            SetupColumns();
            
            // تطبيق الخطوط العربية
            UIHelper.ApplyArabicFont(dgvJournalDetails);
            
            // إعداد الألوان
            dgvJournalDetails.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(240, 248, 255);
            dgvJournalDetails.DefaultCellStyle.SelectionBackColor = Color.FromArgb(51, 122, 183);
            dgvJournalDetails.DefaultCellStyle.SelectionForeColor = Color.White;
        }

        private void SetupColumns()
        {
            dgvJournalDetails.Columns.Clear();
            
            // عمود رقم الحساب
            var accountCodeColumn = new DataGridViewTextBoxColumn
            {
                Name = "AccountCode",
                HeaderText = "رقم الحساب",
                Width = 120,
                ReadOnly = false
            };
            dgvJournalDetails.Columns.Add(accountCodeColumn);
            
            // عمود اسم الحساب
            var accountNameColumn = new DataGridViewTextBoxColumn
            {
                Name = "AccountName",
                HeaderText = "اسم الحساب",
                Width = 200,
                ReadOnly = true
            };
            dgvJournalDetails.Columns.Add(accountNameColumn);
            
            // عمود المدين
            var debitColumn = new DataGridViewTextBoxColumn
            {
                Name = "DebitAmount",
                HeaderText = "مدين",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            };
            dgvJournalDetails.Columns.Add(debitColumn);
            
            // عمود الدائن
            var creditColumn = new DataGridViewTextBoxColumn
            {
                Name = "CreditAmount",
                HeaderText = "دائن",
                Width = 120,
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Format = "N2",
                    Alignment = DataGridViewContentAlignment.MiddleRight
                }
            };
            dgvJournalDetails.Columns.Add(creditColumn);
            
            // عمود المرجع
            var referenceColumn = new DataGridViewTextBoxColumn
            {
                Name = "Reference",
                HeaderText = "المرجع",
                Width = 100
            };
            dgvJournalDetails.Columns.Add(referenceColumn);
            
            // عمود البيان
            var descriptionColumn = new DataGridViewTextBoxColumn
            {
                Name = "Description",
                HeaderText = "البيان التفصيلي",
                Width = 250
            };
            dgvJournalDetails.Columns.Add(descriptionColumn);
            
            // عمود مركز التكلفة
            var costCenterColumn = new DataGridViewComboBoxColumn
            {
                Name = "CostCenterId",
                HeaderText = "مركز التكلفة",
                Width = 150,
                DataSource = _costCenters,
                DisplayMember = "CostCenterNameAr",
                ValueMember = "CostCenterId"
            };
            dgvJournalDetails.Columns.Add(costCenterColumn);
        }
        #endregion

        #region Button Setup
        private void SetupButtons()
        {
            // تطبيق الألوان والخطوط
            UIHelper.ApplyArabicFont(btnNew);
            UIHelper.ApplyArabicFont(btnSave);
            UIHelper.ApplyArabicFont(btnPost);
            UIHelper.ApplyArabicFont(btnDelete);
            UIHelper.ApplyArabicFont(btnPrint);
            UIHelper.ApplyArabicFont(btnExit);
            
            // تعيين الألوان
            btnNew.BackColor = Color.FromArgb(40, 167, 69);
            btnNew.ForeColor = Color.White;
            
            btnSave.BackColor = Color.FromArgb(0, 123, 255);
            btnSave.ForeColor = Color.White;
            
            btnPost.BackColor = Color.FromArgb(255, 193, 7);
            btnPost.ForeColor = Color.Black;
            
            btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            btnDelete.ForeColor = Color.White;
            
            btnPrint.BackColor = Color.FromArgb(108, 117, 125);
            btnPrint.ForeColor = Color.White;
            
            btnExit.BackColor = Color.FromArgb(52, 58, 64);
            btnExit.ForeColor = Color.White;
        }
        #endregion

        #region Journal Entry Management
        private void StartAddMode()
        {
            _isAddMode = true;
            _isEditMode = false;
            _currentJournalEntry = new JournalEntry();
            
            // توليد رقم القيد
            GenerateJournalNumber();
            
            // تعيين التاريخ الحالي
            dtpJournalDate.Value = DateTime.Now;
            
            // تنظيف النموذج
            ClearForm();
            
            // تحديث حالة الأزرار
            UpdateButtonStates();
            
            // التركيز على الوصف العام
            txtGeneralDescription.Focus();
        }

        private void GenerateJournalNumber()
        {
            try
            {
                // TODO: تنفيذ توليد رقم القيد التلقائي
                string journalNumber = $"JE{DateTime.Now:yyyyMM}{DateTime.Now.Day:00}{DateTime.Now.Hour:00}{DateTime.Now.Minute:00}";
                txtJournalNumber.Text = journalNumber;
                _currentJournalEntry.JournalNumber = journalNumber;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد رقم القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void ClearForm()
        {
            txtGeneralDescription.Clear();
            dgvJournalDetails.Rows.Clear();
            lblTotalDebit.Text = "0.00";
            lblTotalCredit.Text = "0.00";
            lblBalance.Text = "0.00";
            lblStatus.Text = "مسودة";
            lblStatus.ForeColor = Color.Orange;
            
            _isDirty = false;
        }

        private void UpdateButtonStates()
        {
            btnSave.Enabled = _isAddMode || _isEditMode;
            btnPost.Enabled = _currentJournalEntry?.Status == JournalStatus.Draft && IsJournalBalanced();
            btnDelete.Enabled = _currentJournalEntry?.JournalEntryId > 0 && _currentJournalEntry?.Status == JournalStatus.Draft;
            btnPrint.Enabled = _currentJournalEntry?.JournalEntryId > 0;
        }

        private bool IsJournalBalanced()
        {
            decimal totalDebit = 0;
            decimal totalCredit = 0;
            
            foreach (DataGridViewRow row in dgvJournalDetails.Rows)
            {
                if (!row.IsNewRow)
                {
                    if (decimal.TryParse(row.Cells["DebitAmount"].Value?.ToString(), out decimal debit))
                        totalDebit += debit;
                    
                    if (decimal.TryParse(row.Cells["CreditAmount"].Value?.ToString(), out decimal credit))
                        totalCredit += credit;
                }
            }
            
            return totalDebit == totalCredit && totalDebit > 0;
        }

        private void CalculateTotals()
        {
            decimal totalDebit = 0;
            decimal totalCredit = 0;

            foreach (DataGridViewRow row in dgvJournalDetails.Rows)
            {
                if (!row.IsNewRow)
                {
                    if (decimal.TryParse(row.Cells["DebitAmount"].Value?.ToString(), out decimal debit))
                        totalDebit += debit;

                    if (decimal.TryParse(row.Cells["CreditAmount"].Value?.ToString(), out decimal credit))
                        totalCredit += credit;
                }
            }

            lblTotalDebit.Text = totalDebit.ToString("N2");
            lblTotalCredit.Text = totalCredit.ToString("N2");

            decimal balance = totalDebit - totalCredit;
            lblBalance.Text = balance.ToString("N2");

            // تغيير لون الرصيد حسب التوازن
            if (balance == 0 && totalDebit > 0)
            {
                lblBalance.ForeColor = Color.Green;
                lblBalance.Text += " (متوازن)";
            }
            else
            {
                lblBalance.ForeColor = Color.Red;
                lblBalance.Text += " (غير متوازن)";
            }

            UpdateButtonStates();
        }
        #endregion

        #region Event Handlers
        private void BtnNew_Click(object sender, EventArgs e)
        {
            try
            {
                if (_isDirty)
                {
                    var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد المتابعة؟", "تأكيد",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                        MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                    if (result == DialogResult.No)
                        return;
                }

                StartAddMode();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء قيد جديد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateJournalEntry())
                    return;

                // إنشاء القيد من البيانات المدخلة
                CreateJournalEntryFromForm();

                // حفظ القيد
                // TODO: تنفيذ حفظ القيد في قاعدة البيانات

                MessageBox.Show("تم حفظ القيد كمسودة بنجاح", "نجح الحفظ",
                    MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                _isDirty = false;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnPost_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateJournalEntry())
                    return;

                if (!IsJournalBalanced())
                {
                    MessageBox.Show("لا يمكن ترحيل قيد غير متوازن", "خطأ في التوازن",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من ترحيل هذا القيد؟\nلن يمكن تعديله بعد الترحيل.", "تأكيد الترحيل",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    // TODO: تنفيذ ترحيل القيد
                    _currentJournalEntry.Status = JournalStatus.Posted;
                    _currentJournalEntry.PostedDate = DateTime.Now;

                    lblStatus.Text = "مرحل";
                    lblStatus.ForeColor = Color.Green;

                    MessageBox.Show("تم ترحيل القيد بنجاح", "نجح الترحيل",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                    UpdateButtonStates();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ترحيل القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentJournalEntry?.JournalEntryId <= 0)
                {
                    MessageBox.Show("لا يوجد قيد محدد للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا القيد؟\nهذا الإجراء لا يمكن التراجع عنه.", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    // TODO: تنفيذ حذف القيد من قاعدة البيانات

                    MessageBox.Show("تم حذف القيد بنجاح", "نجح الحذف",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);

                    StartAddMode();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnPrint_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentJournalEntry?.JournalEntryId <= 0)
                {
                    MessageBox.Show("لا يوجد قيد محدد للطباعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    return;
                }

                // TODO: تنفيذ طباعة القيد
                MessageBox.Show("ميزة الطباعة قيد التطوير", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة القيد: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void DgvJournalDetails_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex < 0)
                    return;

                var row = dgvJournalDetails.Rows[e.RowIndex];
                var columnName = dgvJournalDetails.Columns[e.ColumnIndex].Name;

                // إذا تم تغيير رقم الحساب، ابحث عن اسم الحساب
                if (columnName == "AccountCode")
                {
                    string accountCode = row.Cells["AccountCode"].Value?.ToString();
                    if (!string.IsNullOrEmpty(accountCode))
                    {
                        var account = _accounts.FirstOrDefault(a => a.AccountCode == accountCode);
                        if (account != null)
                        {
                            row.Cells["AccountName"].Value = account.AccountNameAr;
                        }
                        else
                        {
                            row.Cells["AccountName"].Value = "حساب غير موجود";
                            row.Cells["AccountName"].Style.ForeColor = Color.Red;
                        }
                    }
                }

                // إعادة حساب المجاميع عند تغيير المبالغ
                if (columnName == "DebitAmount" || columnName == "CreditAmount")
                {
                    CalculateTotals();
                    _isDirty = true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة تغيير الخلية: {ex.Message}");
            }
        }

        private void DgvJournalDetails_CellValidating(object sender, DataGridViewCellValidatingEventArgs e)
        {
            try
            {
                var columnName = dgvJournalDetails.Columns[e.ColumnIndex].Name;

                // التحقق من صحة المبالغ
                if (columnName == "DebitAmount" || columnName == "CreditAmount")
                {
                    if (!string.IsNullOrEmpty(e.FormattedValue?.ToString()))
                    {
                        if (!decimal.TryParse(e.FormattedValue.ToString(), out decimal amount) || amount < 0)
                        {
                            MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في البيانات",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning,
                                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                            e.Cancel = true;
                        }
                    }
                }

                // التحقق من وجود الحساب
                if (columnName == "AccountCode")
                {
                    string accountCode = e.FormattedValue?.ToString();
                    if (!string.IsNullOrEmpty(accountCode))
                    {
                        var account = _accounts.FirstOrDefault(a => a.AccountCode == accountCode);
                        if (account == null)
                        {
                            MessageBox.Show("رقم الحساب غير موجود", "خطأ في البيانات",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning,
                                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                            e.Cancel = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من صحة الخلية: {ex.Message}");
            }
        }

        private void DgvJournalDetails_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                // F2 لفتح نافذة بحث الحسابات
                if (e.KeyCode == Keys.F2)
                {
                    var currentCell = dgvJournalDetails.CurrentCell;
                    if (currentCell?.OwningColumn.Name == "AccountCode")
                    {
                        OpenAccountLookup();
                        e.Handled = true;
                    }
                }

                // Insert لإضافة سطر جديد
                if (e.KeyCode == Keys.Insert)
                {
                    dgvJournalDetails.Rows.Add();
                    e.Handled = true;
                }

                // Delete لحذف السطر الحالي
                if (e.KeyCode == Keys.Delete && e.Control)
                {
                    if (dgvJournalDetails.CurrentRow != null && !dgvJournalDetails.CurrentRow.IsNewRow)
                    {
                        dgvJournalDetails.Rows.Remove(dgvJournalDetails.CurrentRow);
                        CalculateTotals();
                        _isDirty = true;
                        e.Handled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة ضغط المفاتيح: {ex.Message}");
            }
        }

        /// <summary>
        /// فتح نافذة البحث عن الحسابات
        /// </summary>
        private void OpenAccountLookup()
        {
            try
            {
                if (dgvJournalDetails.CurrentCell == null)
                    return;

                // التحقق من أن العمود الحالي هو عمود رمز الحساب
                if (dgvJournalDetails.CurrentCell.OwningColumn.Name != "AccountCode")
                    return;

                using (var lookupForm = new AccountLookupForm())
                {
                    if (lookupForm.ShowDialog(this) == DialogResult.OK && lookupForm.SelectedAccount != null)
                    {
                        var selectedAccount = lookupForm.SelectedAccount;
                        var currentRow = dgvJournalDetails.CurrentRow;

                        if (currentRow != null)
                        {
                            // تعيين بيانات الحساب
                            currentRow.Cells["AccountCode"].Value = selectedAccount.AccountCode;
                            currentRow.Cells["AccountName"].Value = selectedAccount.AccountNameAr;

                            // حفظ معرف الحساب في Tag
                            currentRow.Tag = selectedAccount.AccountId;

                            // الانتقال لعمود المبلغ المدين
                            if (dgvJournalDetails.Columns.Contains("DebitAmount"))
                            {
                                dgvJournalDetails.CurrentCell = currentRow.Cells["DebitAmount"];
                                dgvJournalDetails.BeginEdit(true);
                            }

                            _isDirty = true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void DgvJournalDetails_UserAddedRow(object sender, DataGridViewRowEventArgs e)
        {
            _isDirty = true;
        }

        private void DgvJournalDetails_UserDeletingRow(object sender, DataGridViewRowCancelEventArgs e)
        {
            _isDirty = true;
            // إعادة حساب المجاميع بعد الحذف
            this.BeginInvoke(new Action(() => CalculateTotals()));
        }

        private void JournalEntryForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (_isDirty)
            {
                var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }

        private void DtpJournalDate_ValueChanged(object sender, EventArgs e)
        {
            _isDirty = true;
        }

        private void CmbJournalType_SelectedIndexChanged(object sender, EventArgs e)
        {
            _isDirty = true;
        }
        #endregion

        #region Validation and Helper Methods
        private bool ValidateJournalEntry()
        {
            // التحقق من وجود وصف عام
            if (string.IsNullOrWhiteSpace(txtGeneralDescription.Text))
            {
                MessageBox.Show("يرجى إدخال الوصف العام للقيد", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtGeneralDescription.Focus();
                return false;
            }

            // التحقق من وجود تفاصيل القيد
            bool hasValidRows = false;
            foreach (DataGridViewRow row in dgvJournalDetails.Rows)
            {
                if (!row.IsNewRow)
                {
                    string accountCode = row.Cells["AccountCode"].Value?.ToString();
                    decimal debit = decimal.TryParse(row.Cells["DebitAmount"].Value?.ToString(), out decimal d) ? d : 0;
                    decimal credit = decimal.TryParse(row.Cells["CreditAmount"].Value?.ToString(), out decimal c) ? c : 0;

                    if (!string.IsNullOrEmpty(accountCode) && (debit > 0 || credit > 0))
                    {
                        hasValidRows = true;
                        break;
                    }
                }
            }

            if (!hasValidRows)
            {
                MessageBox.Show("يرجى إدخال تفاصيل القيد", "خطأ في البيانات",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                return false;
            }

            return true;
        }

        private void CreateJournalEntryFromForm()
        {
            _currentJournalEntry.JournalDate = dtpJournalDate.Value;
            _currentJournalEntry.JournalType = (JournalType)cmbJournalType.SelectedValue;
            _currentJournalEntry.GeneralDescription = txtGeneralDescription.Text;
            _currentJournalEntry.CostCenterId = (int?)cmbCostCenter.SelectedValue;

            // إنشاء تفاصيل القيد
            _currentJournalEntry.JournalEntryDetails.Clear();
            int lineNumber = 1;

            foreach (DataGridViewRow row in dgvJournalDetails.Rows)
            {
                if (!row.IsNewRow)
                {
                    string accountCode = row.Cells["AccountCode"].Value?.ToString();
                    decimal debit = decimal.TryParse(row.Cells["DebitAmount"].Value?.ToString(), out decimal d) ? d : 0;
                    decimal credit = decimal.TryParse(row.Cells["CreditAmount"].Value?.ToString(), out decimal c) ? c : 0;

                    if (!string.IsNullOrEmpty(accountCode) && (debit > 0 || credit > 0))
                    {
                        var account = _accounts.FirstOrDefault(a => a.AccountCode == accountCode);
                        if (account != null)
                        {
                            var detail = new JournalEntryDetail
                            {
                                AccountId = account.AccountId,
                                DebitAmount = debit,
                                CreditAmount = credit,
                                Reference = row.Cells["Reference"].Value?.ToString(),
                                Description = row.Cells["Description"].Value?.ToString(),
                                LineNumber = lineNumber++
                            };

                            _currentJournalEntry.JournalEntryDetails.Add(detail);
                        }
                    }
                }
            }

            // حساب المجاميع
            _currentJournalEntry.TotalDebit = _currentJournalEntry.JournalEntryDetails.Sum(d => d.DebitAmount);
            _currentJournalEntry.TotalCredit = _currentJournalEntry.JournalEntryDetails.Sum(d => d.CreditAmount);
        }
        #endregion
    }
}
