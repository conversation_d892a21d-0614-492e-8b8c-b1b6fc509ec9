using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using AwqafManagement.Models.Accounting;

namespace AwqafManagement.Models
{
    /// <summary>
    /// نموذج القيد اليومي
    /// Journal Entry Model
    /// </summary>
    public class JournalEntry
    {
        public int JournalEntryId { get; set; }
        
        [Required]
        [StringLength(20)]
        public string JournalNumber { get; set; }
        
        [Required]
        public DateTime JournalDate { get; set; }
        
        public DateTime? HijriDate { get; set; }
        
        [Required]
        public JournalType JournalType { get; set; }
        
        [StringLength(500)]
        public string GeneralDescription { get; set; }
        
        public int? CostCenterId { get; set; }
        
        [Required]
        public JournalStatus Status { get; set; }
        
        public decimal TotalDebit { get; set; }
        
        public decimal TotalCredit { get; set; }
        
        public bool IsBalanced => TotalDebit == TotalCredit;
        
        public DateTime CreatedDate { get; set; }
        
        public int CreatedBy { get; set; }
        
        public DateTime? ModifiedDate { get; set; }
        
        public int? ModifiedBy { get; set; }
        
        public DateTime? PostedDate { get; set; }
        
        public int? PostedBy { get; set; }
        
        // Navigation Properties
        public virtual CostCenter CostCenter { get; set; }
        public virtual List<JournalEntryDetail> JournalEntryDetails { get; set; }
        
        public JournalEntry()
        {
            JournalEntryDetails = new List<JournalEntryDetail>();
            JournalDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            Status = JournalStatus.Draft;
            JournalType = JournalType.General;
        }
    }
    
    /// <summary>
    /// تفاصيل القيد اليومي
    /// Journal Entry Detail Model
    /// </summary>
    public class JournalEntryDetail
    {
        public int JournalEntryDetailId { get; set; }
        
        [Required]
        public int JournalEntryId { get; set; }
        
        [Required]
        public int AccountId { get; set; }
        
        public decimal DebitAmount { get; set; }
        
        public decimal CreditAmount { get; set; }
        
        [StringLength(100)]
        public string Reference { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public int? CostCenterId { get; set; }
        
        public int? ProjectId { get; set; }
        
        public int LineNumber { get; set; }
        
        // Navigation Properties
        public virtual JournalEntry JournalEntry { get; set; }
        public virtual ChartOfAccount Account { get; set; }
        public virtual CostCenter CostCenter { get; set; }
    }
    
    /// <summary>
    /// أنواع القيود
    /// Journal Types
    /// </summary>
    public enum JournalType
    {
        [Display(Name = "عادي")]
        General = 1,
        
        [Display(Name = "دوري")]
        Recurring = 2,
        
        [Display(Name = "عكسي")]
        Reversing = 3,
        
        [Display(Name = "إقفال")]
        Closing = 4,
        
        [Display(Name = "تسوية")]
        Adjusting = 5
    }
    
    /// <summary>
    /// حالات القيد
    /// Journal Status
    /// </summary>
    public enum JournalStatus
    {
        [Display(Name = "مسودة")]
        Draft = 1,
        
        [Display(Name = "مرحل")]
        Posted = 2,
        
        [Display(Name = "ملغي")]
        Cancelled = 3,
        
        [Display(Name = "معكوس")]
        Reversed = 4
    }
    
    /// <summary>
    /// مركز التكلفة
    /// Cost Center Model
    /// </summary>
    public class CostCenter
    {
        public int CostCenterId { get; set; }
        
        [Required]
        [StringLength(20)]
        public string CostCenterCode { get; set; }
        
        [Required]
        [StringLength(100)]
        public string CostCenterName { get; set; }
        
        [StringLength(100)]
        public string CostCenterNameAr { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; }
        
        public bool IsActive { get; set; }
        
        public int? ParentCostCenterId { get; set; }
        
        public DateTime CreatedDate { get; set; }
        
        public int CreatedBy { get; set; }
        
        // Navigation Properties
        public virtual CostCenter ParentCostCenter { get; set; }
        public virtual List<CostCenter> SubCostCenters { get; set; }
        
        public CostCenter()
        {
            SubCostCenters = new List<CostCenter>();
            IsActive = true;
            CreatedDate = DateTime.Now;
        }
    }
}
